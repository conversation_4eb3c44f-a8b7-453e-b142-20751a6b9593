#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高DPI显示器测试脚本
用于测试和调试在高分辨率显示器上的图像识别和点击功能
"""

import time
import cv2
import numpy as np
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import os
from PIL import Image
import io

class HighDPITester:
    def __init__(self):
        self.driver = None
        self.setup_chrome_driver()
    
    def setup_chrome_driver(self):
        """设置Chrome浏览器驱动，专门针对高DPI优化"""
        chrome_options = Options()
        
        # 高DPI优化选项
        chrome_options.add_argument("--force-device-scale-factor=1")
        chrome_options.add_argument("--high-dpi-support=1")
        chrome_options.add_argument("--device-scale-factor=1")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        
        # 窗口设置
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 获取显示信息
            self.get_display_info()
            
        except Exception as e:
            print(f"启动Chrome浏览器失败: {e}")
            raise
    
    def get_display_info(self):
        """获取显示器和浏览器信息"""
        print("=" * 60)
        print("显示器和浏览器信息")
        print("=" * 60)
        
        # 获取各种尺寸信息
        info = self.driver.execute_script("""
            return {
                devicePixelRatio: window.devicePixelRatio,
                screenWidth: screen.width,
                screenHeight: screen.height,
                availWidth: screen.availWidth,
                availHeight: screen.availHeight,
                innerWidth: window.innerWidth,
                innerHeight: window.innerHeight,
                outerWidth: window.outerWidth,
                outerHeight: window.outerHeight
            };
        """)
        
        window_size = self.driver.get_window_size()
        
        print(f"设备像素比 (devicePixelRatio): {info['devicePixelRatio']}")
        print(f"屏幕分辨率: {info['screenWidth']}x{info['screenHeight']}")
        print(f"可用屏幕区域: {info['availWidth']}x{info['availHeight']}")
        print(f"浏览器内部视口: {info['innerWidth']}x{info['innerHeight']}")
        print(f"浏览器外部尺寸: {info['outerWidth']}x{info['outerHeight']}")
        print(f"Selenium窗口大小: {window_size['width']}x{window_size['height']}")
        print("=" * 60)
    
    def take_test_screenshot(self):
        """截取测试截图并分析"""
        print("正在截取测试截图...")
        
        # 截图
        screenshot_png = self.driver.get_screenshot_as_png()
        screenshot_pil = Image.open(io.BytesIO(screenshot_png))
        screenshot_cv = cv2.cvtColor(np.array(screenshot_pil), cv2.COLOR_RGB2BGR)
        
        print(f"截图尺寸: {screenshot_cv.shape[1]}x{screenshot_cv.shape[0]}")
        
        # 保存截图
        timestamp = int(time.time())
        screenshot_path = f"test_screenshot_{timestamp}.png"
        cv2.imwrite(screenshot_path, screenshot_cv)
        print(f"测试截图已保存: {screenshot_path}")
        
        return screenshot_cv
    
    def test_template_matching(self):
        """测试模板匹配功能"""
        print("\n开始测试模板匹配...")
        
        # 检查模板文件
        template_files = ['button1.png', 'button2.png', 'button3.png']
        image_dir = os.path.join('states', 'image')
        
        screenshot = self.take_test_screenshot()
        
        for i, template_file in enumerate(template_files, 1):
            template_path = os.path.join(image_dir, template_file)
            
            if not os.path.exists(template_path):
                print(f"❌ 模板文件不存在: {template_path}")
                continue
            
            print(f"\n测试模板 {i}: {template_file}")
            
            # 读取模板
            template = cv2.imread(template_path, cv2.IMREAD_COLOR)
            if template is None:
                print(f"❌ 无法读取模板: {template_path}")
                continue
            
            print(f"模板尺寸: {template.shape[1]}x{template.shape[0]}")
            
            # 多尺度匹配测试
            scales = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]
            best_match = None
            best_val = 0
            best_scale = 1.0
            
            for scale in scales:
                if scale != 1.0:
                    width = int(template.shape[1] * scale)
                    height = int(template.shape[0] * scale)
                    if width > 0 and height > 0 and width < screenshot.shape[1] and height < screenshot.shape[0]:
                        scaled_template = cv2.resize(template, (width, height))
                    else:
                        continue
                else:
                    scaled_template = template
                
                try:
                    result = cv2.matchTemplate(screenshot, scaled_template, cv2.TM_CCOEFF_NORMED)
                    _, max_val, _, max_loc = cv2.minMaxLoc(result)
                    
                    print(f"  缩放 {scale:4.2f}: 匹配度 {max_val:.3f}")
                    
                    if max_val > best_val:
                        best_val = max_val
                        best_match = max_loc
                        best_scale = scale
                        best_template_size = scaled_template.shape[:2]
                
                except Exception as e:
                    print(f"  缩放 {scale:4.2f}: 匹配失败 - {e}")
            
            if best_val > 0.6:
                center_x = best_match[0] + best_template_size[1] // 2
                center_y = best_match[1] + best_template_size[0] // 2
                print(f"✅ 找到匹配: 位置({center_x}, {center_y}), 匹配度{best_val:.3f}, 最佳缩放{best_scale}")
                
                # 在截图上标记找到的位置
                marked_screenshot = screenshot.copy()
                cv2.circle(marked_screenshot, (center_x, center_y), 20, (0, 255, 0), 3)
                cv2.putText(marked_screenshot, f"{template_file}", 
                           (center_x - 50, center_y - 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                
                marked_path = f"marked_{template_file}_{int(time.time())}.png"
                cv2.imwrite(marked_path, marked_screenshot)
                print(f"标记截图已保存: {marked_path}")
            else:
                print(f"❌ 未找到匹配，最高匹配度: {best_val:.3f}")
    
    def run_test(self):
        """运行完整测试"""
        try:
            print("开始高DPI显示器测试...")
            
            # 访问测试页面
            print("\n正在访问测试页面...")
            self.driver.get("https://app.tangoapp.dev/guide")
            
            # 设置页面缩放
            self.driver.execute_script("document.body.style.zoom='100%'")
            
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            time.sleep(3)  # 等待页面完全加载
            
            # 滚动到底部
            print("\n滚动到页面底部...")
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            
            # 测试模板匹配
            self.test_template_matching()
            
            print("\n测试完成！请检查生成的截图文件。")
            print("浏览器将在30秒后关闭，您可以手动检查页面...")
            time.sleep(30)
            
        except Exception as e:
            print(f"测试过程中出错: {e}")
        finally:
            if self.driver:
                self.driver.quit()

def main():
    """主函数"""
    tester = HighDPITester()
    tester.run_test()

if __name__ == "__main__":
    main()
