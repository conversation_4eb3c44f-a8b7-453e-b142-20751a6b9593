#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行脚本 - 自动安装依赖并运行浏览器自动化
"""

import subprocess
import sys
import os

def install_requirements():
    """安装依赖包"""
    print("正在安装Python依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖包安装完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"安装依赖包失败: {e}")
        return False

def run_automation():
    """运行自动化脚本"""
    print("\n开始运行浏览器自动化脚本...")
    try:
        subprocess.run([sys.executable, "browser_automation.py"])
        print("脚本执行完成！")
    except Exception as e:
        print(f"运行脚本时出错: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("Chrome浏览器自动化脚本运行器")
    print("=" * 50)
    
    # 检查必要文件是否存在
    required_files = [
        "browser_automation.py",
        "requirements.txt",
        "states/image/button1.png",
        "states/image/button2.png", 
        "states/image/button3.png"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("错误：以下必要文件缺失：")
        for file_path in missing_files:
            print(f"  - {file_path}")
        print("\n请确保所有文件都存在后再运行此脚本。")
        input("按回车键退出...")
        return
    
    # 安装依赖
    if not install_requirements():
        input("按回车键退出...")
        return
    
    # 运行自动化脚本
    run_automation()
    
    print("\n" + "=" * 50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
