# Chrome浏览器自动化脚本

这个脚本会自动打开Chrome浏览器，访问指定网页，滚动到底部，并根据图片模板依次点击按钮。

## 功能特性

- 自动访问 https://app.tangoapp.dev/guide
- 智能滚动到页面底部
- 使用图像识别技术定位按钮
- 按顺序点击三个按钮（间隔1秒）
- 支持动态页面内容加载

## 环境要求

- Python 3.7+
- Chrome浏览器
- ChromeDriver

## 安装步骤

### 1. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 2. 安装ChromeDriver

#### 方法一：自动安装（推荐）
```bash
pip install webdriver-manager
```

然后修改脚本中的driver初始化部分使用webdriver-manager。

#### 方法二：手动安装
1. 访问 https://chromedriver.chromium.org/
2. 下载与您的Chrome版本匹配的ChromeDriver
3. 将ChromeDriver.exe放到PATH环境变量中的任意目录，或放到脚本同目录下

### 3. 准备图片文件

确保以下图片文件存在于 `states/image/` 目录中：
- button1.png
- button2.png  
- button3.png

## 使用方法

运行脚本：

```bash
python browser_automation.py
```

## 脚本工作流程

1. **启动Chrome浏览器** - 使用最大化窗口模式
2. **访问目标网页** - https://app.tangoapp.dev/guide
3. **等待页面加载** - 确保页面完全加载
4. **滚动到底部** - 智能检测页面高度变化
5. **图像识别点击** - 依次识别并点击三个按钮
   - 寻找button1.png对应的按钮并点击
   - 等待1秒
   - 寻找button2.png对应的按钮并点击  
   - 等待1秒
   - 寻找button3.png对应的按钮并点击
6. **完成操作** - 保持浏览器打开10秒后自动关闭

## 注意事项

- 确保图片文件清晰，与网页上的按钮匹配度高
- 如果按钮未找到，脚本会跳过该按钮继续执行
- 脚本会在控制台输出详细的执行日志
- 可以根据需要调整图像匹配的阈值（默认0.8）

## 故障排除

### 常见问题

1. **ChromeDriver版本不匹配**
   - 确保ChromeDriver版本与Chrome浏览器版本兼容

2. **图片识别失败**
   - 检查图片文件是否存在且格式正确
   - 尝试降低匹配阈值（修改threshold值）
   - 确保图片与网页上的按钮外观一致

3. **页面加载超时**
   - 检查网络连接
   - 增加等待时间

4. **高DPI显示器问题（重要！）**
   - 如果您使用高分辨率显示器（如2880×1800）且缩放比例不是100%，可能会遇到点击位置不准确的问题
   - 脚本已经包含了多种高DPI适配方案：
     - 强制设备缩放因子为1
     - 多尺度模板匹配
     - 多种点击方式
   - 运行测试脚本进行诊断：`python test_high_dpi.py`
   - 查看生成的调试截图文件来确认按钮是否被正确识别

### 高DPI显示器专用测试

如果在高分辨率显示器上遇到问题，请运行专用测试脚本：

```bash
python test_high_dpi.py
```

这个脚本会：
- 显示详细的显示器和浏览器信息
- 测试多尺度模板匹配
- 生成带标记的调试截图
- 帮助诊断高DPI相关问题

## 自定义配置

可以在脚本中修改以下参数：
- `threshold`: 图像匹配阈值（0.0-1.0）
- 等待时间间隔
- 浏览器选项
- 目标URL
