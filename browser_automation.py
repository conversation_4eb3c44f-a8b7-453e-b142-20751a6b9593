#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome浏览器自动化脚本
功能：访问指定网页，滚动到底部，并根据图片模板依次点击按钮
"""

import time
import cv2
import numpy as np
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import os
import sys
from PIL import Image
import io

class BrowserAutomation:
    def __init__(self):
        self.driver = None
        self.setup_chrome_driver()
    
    def setup_chrome_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")  # 最大化窗口
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        try:
            # 自动下载和管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            print("Chrome浏览器启动成功")
        except Exception as e:
            print(f"启动Chrome浏览器失败: {e}")
            print("请确保已安装Chrome浏览器")
            sys.exit(1)
    
    def navigate_to_page(self, url):
        """导航到指定页面"""
        try:
            print(f"正在访问: {url}")
            self.driver.get(url)
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            print("页面加载完成")
            time.sleep(2)  # 额外等待确保页面完全加载
        except TimeoutException:
            print("页面加载超时")
        except Exception as e:
            print(f"访问页面时出错: {e}")
    
    def scroll_to_bottom(self):
        """滚动到页面底部"""
        print("开始滚动到页面底部...")
        
        # 获取页面总高度
        last_height = self.driver.execute_script("return document.body.scrollHeight")
        
        while True:
            # 滚动到底部
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            
            # 等待新内容加载
            time.sleep(2)
            
            # 计算新的滚动高度并与上次滚动高度进行比较
            new_height = self.driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                break
            last_height = new_height
        
        print("已滚动到页面底部")
    
    def take_screenshot(self):
        """截取当前页面截图"""
        screenshot = self.driver.get_screenshot_as_png()
        screenshot_image = Image.open(io.BytesIO(screenshot))
        screenshot_cv = cv2.cvtColor(np.array(screenshot_image), cv2.COLOR_RGB2BGR)
        return screenshot_cv
    
    def find_button_location(self, template_path, screenshot=None):
        """使用模板匹配找到按钮位置"""
        if not os.path.exists(template_path):
            print(f"模板图片不存在: {template_path}")
            return None
        
        # 读取模板图片
        template = cv2.imread(template_path, cv2.IMREAD_COLOR)
        if template is None:
            print(f"无法读取模板图片: {template_path}")
            return None
        
        # 如果没有提供截图，则截取当前页面
        if screenshot is None:
            screenshot = self.take_screenshot()
        
        # 模板匹配
        result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        
        # 设置匹配阈值
        threshold = 0.8
        if max_val >= threshold:
            # 计算按钮中心点
            template_height, template_width = template.shape[:2]
            center_x = max_loc[0] + template_width // 2
            center_y = max_loc[1] + template_height // 2
            
            print(f"找到按钮位置: ({center_x}, {center_y}), 匹配度: {max_val:.3f}")
            return (center_x, center_y)
        else:
            print(f"未找到匹配的按钮，最高匹配度: {max_val:.3f}")
            return None
    
    def click_at_position(self, x, y):
        """在指定位置点击"""
        try:
            # 使用JavaScript点击指定坐标
            self.driver.execute_script(f"""
                var element = document.elementFromPoint({x}, {y});
                if (element) {{
                    element.click();
                    console.log('点击了元素:', element);
                }} else {{
                    console.log('在坐标 ({x}, {y}) 处未找到元素');
                }}
            """)
            print(f"已点击坐标: ({x}, {y})")
        except Exception as e:
            print(f"点击时出错: {e}")
    
    def click_buttons_sequence(self):
        """按顺序点击三个按钮"""
        button_files = ['button1.png', 'button2.png', 'button3.png']
        
        for i, button_file in enumerate(button_files, 1):
            template_path = os.path.join('states', 'image', button_file)
            print(f"\n正在寻找并点击按钮 {i}: {button_file}")
            
            # 截取当前页面截图
            screenshot = self.take_screenshot()
            
            # 查找按钮位置
            button_location = self.find_button_location(template_path, screenshot)
            
            if button_location:
                # 点击按钮
                self.click_at_position(button_location[0], button_location[1])
                print(f"按钮 {i} 点击完成")
                
                # 等待1秒
                if i < len(button_files):  # 最后一个按钮后不需要等待
                    print("等待1秒...")
                    time.sleep(1)
            else:
                print(f"未找到按钮 {i}，跳过")
    
    def run(self):
        """运行主流程"""
        try:
            # 1. 访问指定网页
            self.navigate_to_page("https://app.tangoapp.dev/guide")
            
            # 2. 滚动到页面底部
            self.scroll_to_bottom()
            
            # 3. 依次点击按钮
            self.click_buttons_sequence()
            
            print("\n脚本执行完成！")
            
        except Exception as e:
            print(f"执行过程中出错: {e}")
        finally:
            # 保持浏览器打开一段时间以便查看结果
            print("浏览器将在10秒后关闭...")
            time.sleep(10)
            self.close()
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("浏览器已关闭")

def main():
    """主函数"""
    print("开始执行浏览器自动化脚本...")
    
    # 检查图片文件是否存在
    image_dir = os.path.join('states', 'image')
    required_files = ['button1.png', 'button2.png', 'button3.png']
    
    for file_name in required_files:
        file_path = os.path.join(image_dir, file_name)
        if not os.path.exists(file_path):
            print(f"错误: 找不到图片文件 {file_path}")
            return
    
    # 创建自动化实例并运行
    automation = BrowserAutomation()
    automation.run()

if __name__ == "__main__":
    main()
