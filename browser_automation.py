#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome浏览器自动化脚本
功能：访问指定网页，滚动到底部，并根据图片模板依次点击按钮
"""

import time
import cv2
import numpy as np
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import os
import sys
from PIL import Image
import io

class BrowserAutomation:
    def __init__(self):
        self.driver = None
        self.device_pixel_ratio = 1.0  # 设备像素比
        self.setup_chrome_driver()
    
    def setup_chrome_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")  # 最大化窗口
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--force-device-scale-factor=1")  # 强制设备缩放比为1
        chrome_options.add_argument("--high-dpi-support=1")  # 启用高DPI支持
        chrome_options.add_argument("--device-scale-factor=1")  # 设备缩放因子为1
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        try:
            # 自动下载和管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # 获取设备像素比
            self.device_pixel_ratio = self.driver.execute_script("return window.devicePixelRatio")
            print(f"Chrome浏览器启动成功，设备像素比: {self.device_pixel_ratio}")
        except Exception as e:
            print(f"启动Chrome浏览器失败: {e}")
            print("请确保已安装Chrome浏览器")
            sys.exit(1)
    
    def navigate_to_page(self, url):
        """导航到指定页面"""
        try:
            print(f"正在访问: {url}")
            self.driver.get(url)

            # 设置页面缩放为100%，避免高DPI问题
            self.driver.execute_script("document.body.style.zoom='100%'")

            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # 获取并打印页面信息
            window_size = self.driver.get_window_size()
            viewport_size = self.driver.execute_script("return {width: window.innerWidth, height: window.innerHeight}")
            print(f"页面加载完成")
            print(f"窗口大小: {window_size['width']}x{window_size['height']}")
            print(f"视口大小: {viewport_size['width']}x{viewport_size['height']}")
            print(f"设备像素比: {self.device_pixel_ratio}")

            time.sleep(2)  # 额外等待确保页面完全加载
        except TimeoutException:
            print("页面加载超时")
        except Exception as e:
            print(f"访问页面时出错: {e}")
    
    def scroll_to_bottom(self):
        """滚动到页面底部"""
        print("开始滚动到页面底部...")
        
        # 获取页面总高度
        last_height = self.driver.execute_script("return document.body.scrollHeight")
        
        while True:
            # 滚动到底部
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            
            # 等待新内容加载
            time.sleep(2)
            
            # 计算新的滚动高度并与上次滚动高度进行比较
            new_height = self.driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                break
            last_height = new_height
        
        print("已滚动到页面底部")
    
    def take_screenshot(self):
        """截取当前页面截图"""
        screenshot = self.driver.get_screenshot_as_png()
        screenshot_image = Image.open(io.BytesIO(screenshot))
        screenshot_cv = cv2.cvtColor(np.array(screenshot_image), cv2.COLOR_RGB2BGR)
        return screenshot_cv
    
    def find_button_location(self, template_path, screenshot=None):
        """使用多尺度模板匹配找到按钮位置，适配高DPI显示器"""
        if not os.path.exists(template_path):
            print(f"模板图片不存在: {template_path}")
            return None

        # 读取模板图片
        template = cv2.imread(template_path, cv2.IMREAD_COLOR)
        if template is None:
            print(f"无法读取模板图片: {template_path}")
            return None

        # 如果没有提供截图，则截取当前页面
        if screenshot is None:
            screenshot = self.take_screenshot()

        print(f"截图尺寸: {screenshot.shape[:2]}, 模板尺寸: {template.shape[:2]}")

        # 多尺度模板匹配，适应不同的缩放比例
        scales = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]  # 不同的缩放比例
        best_match = None
        best_val = 0
        best_scale = 1.0

        for scale in scales:
            # 缩放模板
            if scale != 1.0:
                width = int(template.shape[1] * scale)
                height = int(template.shape[0] * scale)
                if width > 0 and height > 0 and width < screenshot.shape[1] and height < screenshot.shape[0]:
                    scaled_template = cv2.resize(template, (width, height))
                else:
                    continue
            else:
                scaled_template = template

            # 模板匹配
            try:
                result = cv2.matchTemplate(screenshot, scaled_template, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, max_loc = cv2.minMaxLoc(result)

                print(f"缩放比例 {scale}: 最高匹配度 {max_val:.3f}")

                if max_val > best_val:
                    best_val = max_val
                    best_match = max_loc
                    best_scale = scale
                    best_template_size = scaled_template.shape[:2]
            except Exception as e:
                print(f"缩放比例 {scale} 匹配失败: {e}")
                continue

        # 设置匹配阈值
        threshold = 0.6  # 降低阈值以适应高DPI显示器
        if best_val >= threshold and best_match is not None:
            # 计算按钮中心点
            template_height, template_width = best_template_size
            center_x = best_match[0] + template_width // 2
            center_y = best_match[1] + template_height // 2

            print(f"找到按钮位置: ({center_x}, {center_y}), 最佳匹配度: {best_val:.3f}, 最佳缩放: {best_scale}")
            return (center_x, center_y)
        else:
            print(f"未找到匹配的按钮，最高匹配度: {best_val:.3f}")
            return None
    
    def click_at_position(self, x, y):
        """在指定位置点击，支持多种点击方式"""
        try:
            # 方法1: 使用JavaScript点击指定坐标
            print(f"尝试方法1: JavaScript点击坐标 ({x}, {y})")
            element_found = self.driver.execute_script(f"""
                var element = document.elementFromPoint({x}, {y});
                if (element) {{
                    element.click();
                    console.log('点击了元素:', element.tagName, element.className, element.id);
                    return true;
                }} else {{
                    console.log('在坐标 ({x}, {y}) 处未找到元素');
                    return false;
                }}
            """)

            if element_found:
                print(f"方法1成功: 已点击坐标 ({x}, {y})")
                return True

            # 方法2: 使用ActionChains点击
            print(f"尝试方法2: ActionChains点击")
            from selenium.webdriver.common.action_chains import ActionChains
            actions = ActionChains(self.driver)
            actions.move_by_offset(x, y).click().perform()
            actions.reset_actions()  # 重置动作链
            print(f"方法2完成: ActionChains点击坐标 ({x}, {y})")
            return True

        except Exception as e:
            print(f"点击时出错: {e}")
            return False
    
    def save_debug_screenshot(self, screenshot, button_location, button_name):
        """保存调试截图，标记找到的按钮位置"""
        try:
            debug_screenshot = screenshot.copy()
            if button_location:
                # 在找到的位置画一个红色圆圈
                cv2.circle(debug_screenshot, button_location, 20, (0, 0, 255), 3)
                cv2.putText(debug_screenshot, f"Found: {button_name}",
                           (button_location[0] - 50, button_location[1] - 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

            # 保存调试截图
            debug_path = f"debug_{button_name}_{int(time.time())}.png"
            cv2.imwrite(debug_path, debug_screenshot)
            print(f"调试截图已保存: {debug_path}")
        except Exception as e:
            print(f"保存调试截图失败: {e}")

    def click_buttons_sequence(self):
        """按顺序点击三个按钮"""
        button_files = ['button1.png', 'button2.png', 'button3.png']

        for i, button_file in enumerate(button_files, 1):
            template_path = os.path.join('states', 'image', button_file)
            print(f"\n{'='*50}")
            print(f"正在寻找并点击按钮 {i}: {button_file}")
            print(f"{'='*50}")

            # 截取当前页面截图
            screenshot = self.take_screenshot()

            # 查找按钮位置
            button_location = self.find_button_location(template_path, screenshot)

            # 保存调试截图
            self.save_debug_screenshot(screenshot, button_location, f"button{i}")

            if button_location:
                # 点击按钮
                success = self.click_at_position(button_location[0], button_location[1])
                if success:
                    print(f"✅ 按钮 {i} 点击完成")
                else:
                    print(f"❌ 按钮 {i} 点击失败")

                # 等待1秒
                if i < len(button_files):  # 最后一个按钮后不需要等待
                    print("等待1秒...")
                    time.sleep(1)
            else:
                print(f"❌ 未找到按钮 {i}，跳过")
                # 即使没找到也等待一下，避免操作过快
                time.sleep(0.5)
    
    def run(self):
        """运行主流程"""
        try:
            # 1. 访问指定网页
            self.navigate_to_page("https://app.tangoapp.dev/guide")
            
            # 2. 滚动到页面底部
            self.scroll_to_bottom()
            
            # 3. 依次点击按钮
            self.click_buttons_sequence()
            
            print("\n脚本执行完成！")
            
        except Exception as e:
            print(f"执行过程中出错: {e}")
        finally:
            # 保持浏览器打开一段时间以便查看结果
            print("浏览器将在10秒后关闭...")
            time.sleep(10)
            self.close()
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("浏览器已关闭")

def main():
    """主函数"""
    print("开始执行浏览器自动化脚本...")
    
    # 检查图片文件是否存在
    image_dir = os.path.join('states', 'image')
    required_files = ['button1.png', 'button2.png', 'button3.png']
    
    for file_name in required_files:
        file_path = os.path.join(image_dir, file_name)
        if not os.path.exists(file_path):
            print(f"错误: 找不到图片文件 {file_path}")
            return
    
    # 创建自动化实例并运行
    automation = BrowserAutomation()
    automation.run()

if __name__ == "__main__":
    main()
